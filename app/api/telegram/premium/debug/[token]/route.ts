import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// Force dynamic rendering - prevent static caching
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const supabase = getServiceRoleClient()
    const { token: rawToken } = await params

    console.log('Debug - Raw token from URL:', rawToken)
    console.log('Debug - Raw token length:', rawToken.length)

    const decodedToken = decodeURIComponent(rawToken)
    console.log('Debug - Decoded token:', decodedToken)
    console.log('Debug - Decoded token length:', decodedToken.length)

    // Search for bot configuration
    const { data: botConfig, error } = await supabase
      .from('bot_configurations')
      .select('id, bot_token, bot_username, company_id')
      .eq('bot_token', decodedToken)
      .single()

    console.log('Debug - Database query result:', { botConfig, error })

    // Test the exact query from the health check endpoint
    const { data: botConfig2, error: error2 } = await supabase
      .from('bot_configurations')
      .select(`
        id,
        bot_username,
        is_active,
        last_activity,
        last_health_check,
        message_count,
        companies!bot_configurations_company_id_fkey(name)
      `)
      .eq('bot_token', decodedToken)
      .single()

    console.log('Debug - Health check query result:', { botConfig2, error2 })

    // Return debug info
    return NextResponse.json({
      debug: {
        rawToken,
        rawTokenLength: rawToken.length,
        decodedToken,
        decodedTokenLength: decodedToken.length,
        tokensEqual: rawToken === decodedToken,
        databaseResult: {
          simple: { found: !!botConfig, error: error?.message },
          healthCheck: { found: !!botConfig2, error: error2?.message }
        }
      }
    })

  } catch (error) {
    console.error('Debug endpoint error:', error)
    return NextResponse.json({
      error: 'Debug error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
