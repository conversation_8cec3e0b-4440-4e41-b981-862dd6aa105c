import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function POST(request: Request) {
  try {
    const { memberId, companyId } = await request.json()

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Get member info
    const { data: member, error: memberError } = await supabase
      .from('loyalty_members')
      .select('name, phone_number, company_id, telegram_chat_id')
      .eq('id', memberId)
      .single()

    if (memberError || !member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    // If no specific companyId provided, unlink from member's primary company
    const targetCompanyId = companyId || member.company_id

    // Get all active bot links for this member
    const { data: botLinks, error: linksError } = await supabase
      .from('telegram_member_bot_links')
      .select(`
        id,
        telegram_chat_id,
        telegram_username,
        company_id,
        companies:company_id(name)
      `)
      .eq('member_id', memberId)
      .eq('is_active', true)

    if (linksError) {
      console.error('Bot links fetch error:', linksError)
      return NextResponse.json({ error: 'Failed to fetch bot links' }, { status: 500 })
    }

    if (!botLinks || botLinks.length === 0) {
      return NextResponse.json({
        error: 'Member is not linked to any Telegram bots',
        member: { name: member.name, phone_number: member.phone_number }
      }, { status: 400 })
    }

    // Find the specific bot link to unlink
    const targetBotLink = botLinks.find(link => link.company_id === targetCompanyId)

    if (!targetBotLink) {
      return NextResponse.json({
        error: `Member is not linked to the specified company via Telegram`,
        availableLinks: botLinks.map(link => ({
          companyId: link.company_id,
          companyName: (link.companies as { name?: string })?.name
        }))
      }, { status: 400 })
    }

    // Deactivate the specific bot link
    const { error: unlinkError } = await supabase
      .from('telegram_member_bot_links')
      .update({
        is_active: false,
        last_interaction: new Date().toISOString()
      })
      .eq('id', targetBotLink.id)

    if (unlinkError) {
      console.error('Unlink error:', unlinkError)
      return NextResponse.json({ error: 'Failed to unlink member from bot' }, { status: 500 })
    }

    // If unlinking from primary company, also clear loyalty_members telegram fields for backward compatibility
    if (targetCompanyId === member.company_id) {
      await supabase
        .from('loyalty_members')
        .update({
          telegram_chat_id: null,
          telegram_username: null,
          linking_token: null,
          linked_at: null
        })
        .eq('id', memberId)
    }

    const companyName = (targetBotLink.companies as { name?: string })?.name || 'Unknown Company'

    console.log(`✅ Successfully unlinked member ${member.name} (${memberId}) from company ${companyName} (${targetCompanyId})`)

    // Get remaining active links
    const remainingLinks = botLinks.filter(link => link.id !== targetBotLink.id)

    return NextResponse.json({
      success: true,
      message: `Member ${member.name} successfully unlinked from ${companyName}`,
      member: { name: member.name, phone_number: member.phone_number },
      unlinkedFrom: {
        companyId: targetCompanyId,
        companyName,
        chatId: targetBotLink.telegram_chat_id,
        username: targetBotLink.telegram_username
      },
      remainingLinks: remainingLinks.length,
      remainingLinkDetails: remainingLinks.map(link => ({
        companyId: link.company_id,
        companyName: (link.companies as { name?: string })?.name
      }))
    })

  } catch (error) {
    console.error('Unlink error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
