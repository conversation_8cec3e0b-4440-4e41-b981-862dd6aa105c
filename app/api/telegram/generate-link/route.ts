import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { randomBytes } from 'crypto'

export async function POST(request: Request) {
  try {
    const { memberId } = await request.json()

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Generate unique linking token
    const linkingToken = randomBytes(16).toString('hex')

    // Update member with linking token and get company info
    const { data, error } = await supabase
      .from('loyalty_members')
      .update({ linking_token: linkingToken })
      .eq('id', memberId)
      .select('phone_number, name, company_id')
      .single()

    if (error) {
      console.error('Token generation error:', error)
      return NextResponse.json({ error: 'Failed to generate token' }, { status: 500 })
    }

    // Check if the company has a premium bot configured
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('bot_tier, bot_configuration_id')
      .eq('id', data.company_id)
      .single()

    if (companyError) {
      console.error('Company lookup error:', companyError)
      // Fallback to standard bot if company lookup fails
    }

    // Determine which bot to use
    let botUsername = process.env.TELEGRAM_BOT_USERNAME // Default to standard bot
    
    if (companyData?.bot_tier === 'premium' && companyData?.bot_configuration_id) {
      // Get bot configuration separately
      const { data: botConfig, error: botError } = await supabase
        .from('bot_configurations')
        .select('bot_username')
        .eq('id', companyData.bot_configuration_id)
        .single()

      if (botError) {
        console.error('Bot configuration lookup error:', botError)
      } else if (botConfig?.bot_username) {
        botUsername = botConfig.bot_username
      }
    }

    const telegramLink = `https://t.me/${botUsername}?start=${linkingToken}`

    return NextResponse.json({
      linkingToken,
      link: telegramLink, // Changed from telegramLink to link
      telegramLink, // Keep this for backward compatibility
      instructions: `Send this link to member: ${telegramLink}`,
      member: data,
      botType: companyData?.bot_tier === 'premium' ? 'premium' : 'standard',
      botUsername
    })

  } catch (error) {
    console.error('Token generation error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const memberId = searchParams.get('memberId')

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Check if member already has an active linking token
    const { data: member, error } = await supabase
      .from('loyalty_members')
      .select('linking_token, name, phone_number, telegram_chat_id, company_id')
      .eq('id', memberId)
      .single()

    if (error) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    // Check existing bot links for this member
    const { data: botLinks } = await supabase
      .from('telegram_member_bot_links')
      .select(`
        id,
        telegram_chat_id,
        telegram_username,
        company_id,
        bot_configuration_id,
        is_active,
        companies(name),
        bot_configurations(bot_username, bot_name)
      `)
      .eq('member_id', memberId)
      .eq('is_active', true)

    return NextResponse.json({
      hasToken: !!member.linking_token,
      isLinked: !!member.telegram_chat_id,
      member: {
        name: member.name,
        phone_number: member.phone_number
      },
      botLinks: botLinks || [],
      totalBotLinks: botLinks?.length || 0
    })

  } catch (error) {
    console.error('Token check error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
