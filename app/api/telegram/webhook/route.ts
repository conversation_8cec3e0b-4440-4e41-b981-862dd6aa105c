import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { sendTelegramMessage, type TelegramUpdate } from '@/lib/telegram'
import { generateText } from 'ai'
import { google } from '@ai-sdk/google'
import { z } from 'zod'

// TypeScript interfaces for bot link data structures
interface LoyaltyMember {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  telegram_chat_id?: string;
  telegram_username?: string;
  company_id: string;
  registration_date: string;
  linked_at?: string;
  linking_token?: string;
  lifetime_points: number;
  redeemed_points: number;
  expired_points: number;
  loyalty_tier?: string;
}

interface Company {
  id: string;
  name: string;
  points_earning_ratio: number;
  bot_tier?: string;
  bot_configuration_id?: string;
}

interface BotConfiguration {
  id: string;
  bot_username: string;
  bot_name?: string;
}

interface BotLink {
  id: string;
  telegram_chat_id: string;
  telegram_username?: string;
  member_id: string;
  company_id: string;
  bot_configuration_id?: string;
  created_at: string;
  last_interaction: string;
  is_active: boolean;
  preferences?: Record<string, unknown>;
  loyalty_members?: LoyaltyMember;
  companies?: Company;
  bot_configurations?: BotConfiguration;
}

// Helper function to get member by chat ID using multi-bot structure
async function getMemberByChatId(chatId: string): Promise<BotLink | null> {
  const supabase = getServiceRoleClient()

  const { data: botLink } = await supabase
    .from('telegram_member_bot_links')
    .select(`
      *,
      loyalty_members(*),
      companies:company_id(name, points_earning_ratio, bot_tier),
      bot_configurations(bot_username, bot_name)
    `)
    .eq('telegram_chat_id', chatId)
    .eq('is_active', true)
    .order('last_interaction', { ascending: false })
    .limit(1)
    .maybeSingle()

  return botLink
}

// Webhook handler for Telegram bot
export async function POST(request: Request): Promise<Response> {
  try {
    const update: TelegramUpdate = await request.json()

    if (!update.message || !update.message.text) {
      return NextResponse.json({ ok: true })
    }

    const message = update.message
    const chatId = message.chat.id
    const text = message.text
    const username = message.from?.username

    console.log(`Telegram message from ${username} (${chatId}): ${text}`)

    // Handle bot commands
    if (text?.startsWith('/')) {
      await handleCommand(chatId, text, username)
    } else if (text) {
      // Handle natural language conversation with AI
      await handleConversation(chatId, text, username)
    }

    return NextResponse.json({ ok: true })
  } catch (error) {
    console.error('Telegram webhook error:', error)
    return NextResponse.json({ error: 'Webhook failed' }, { status: 500 })
  }
}

async function handleCommand(chatId: number, command: string, username?: string): Promise<void> {
  console.log(`[Telegram] Processing command: "${command}" for chat ${chatId}`)

  // Handle /start command with optional token
  if (command.toLowerCase().startsWith('/start')) {
    const parts = command.split(' ')
    if (parts.length > 1) {
      const token = parts[1]

      // Check if it's a cashier invitation token
      if (token.startsWith('cashier_')) {
        const invitationToken = token.substring(8) // Remove 'cashier_' prefix
        await handleCashierInvitation(chatId, invitationToken)
        return
      } else {
        // Handle member linking token
        await linkMemberAccount(chatId, token, username)
        return
      }
    } else {
      // No token provided, send welcome message
      await sendWelcomeMessage(chatId)
      return
    }
  }

  // Check for /unlink with number parameter (e.g., /unlink 2)
  if (command.toLowerCase().startsWith('/unlink ')) {
    const parts = command.split(' ')
    if (parts.length > 1) {
      const unlinkNumber = parseInt(parts[1])
      if (!isNaN(unlinkNumber)) {
        await handleAccountUnlinkWithNumber(chatId, unlinkNumber, username)
        return
      }
    }
  }

  switch (command.toLowerCase().trim()) {
    case '/link':
      await sendLinkingInstructions(chatId)
      break

    case '/balance':
      await sendPointsBalance(chatId)
      break

    case '/rewards':
      await sendAvailableRewards(chatId)
      break

    case '/history':
      await sendTransactionHistory(chatId)
      break

    case '/profile':
      await sendProfileInfo(chatId)
      break

    case '/help':
      await sendHelpMessage(chatId)
      break

    case '/settings':
      await sendSettingsMenu(chatId)
      break

    case '/tier':
      console.log(`[Telegram] Executing /tier command for chat ${chatId}`)
      await sendTierInfo(chatId)
      break

    case '/unlink':
      await handleAccountUnlink(chatId, username)
      break

    default:
      console.log(`[Telegram] Unknown command: "${command}" for chat ${chatId}`)
      await sendTelegramMessage(chatId,
        "I don't recognize that command. Type /help to see available commands."
      )
  }
}

async function handleConversation(chatId: number, text: string, username?: string): Promise<void> {
  const supabase = getServiceRoleClient()

  // Get member by chat_id using the new multi-bot structure
  const botLink = await getMemberByChatId(chatId.toString())

  if (!botLink?.loyalty_members) {
    await sendTelegramMessage(chatId,
      "🔗 You need to link your account first. Use /link to get started!"
    )
    return
  }

  const member = botLink.loyalty_members

  // Store conversation in database
  await supabase
    .from('telegram_conversations')
    .insert({
      member_id: member.id,
      chat_id: chatId.toString(),
      message_type: 'user',
      message_text: text,
      username: username || null
    })

  // Generate AI response with context about the user
  try {
    const startTime = Date.now()
    const { text: aiResponse } = await generateText({
      model: google('gemini-2.0-flash'),
      system: `You are a helpful AI assistant for ${botLink.companies?.name || 'the loyalty program'}.

      User Info:
      - Name: ${member.name}
      - Current Points: ${member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)}
      - Lifetime Points: ${member.lifetime_points}
      - Loyalty Tier: ${member.loyalty_tier || 'Standard'}
      - Company: ${botLink.companies?.name}

      You can help with:
      - Points balance and history
      - Available rewards
      - Loyalty program questions
      - Account information

      Keep responses friendly, helpful, and concise. Use emojis appropriately.
      If they ask about specific actions (like redeeming rewards), guide them to use the appropriate commands.`,
      messages: [{ role: 'user', content: text }],
      tools: {
        getPointsBalance: {
          description: 'Get the current points balance for the user',
          parameters: z.object({}),
          execute: async () => {
            const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)
            return {
              currentPoints,
              lifetimePoints: member.lifetime_points,
              redeemedPoints: member.redeemed_points || 0,
              tier: member.loyalty_tier || 'Standard'
            }
          }
        },
        getRecentTransactions: {
          description: 'Get recent points transactions for the user',
          parameters: z.object({
            limit: z.number().optional().default(5)
          }),
          execute: async ({ limit }) => {
            const { data: transactions } = await supabase
              .from('points_transactions')
              .select('*')
              .eq('member_id', member.id)
              .order('transaction_date', { ascending: false })
              .limit(limit)

            return transactions || []
          }
        },
        getAvailableRewards: {
          description: 'Get available rewards for the user',
          parameters: z.object({}),
          execute: async () => {
            const { data: rewards } = await supabase
              .from('rewards')
              .select('*')
              .eq('company_id', member.company_id)
              .eq('is_active', true)
              .order('points_required', { ascending: true })

            return rewards || []
          }
        }
      },
      maxSteps: 3
    })

    const responseTime = Date.now() - startTime

    // Store AI response in database
    await supabase
      .from('telegram_conversations')
      .insert({
        member_id: member.id,
        chat_id: chatId.toString(),
        message_type: 'bot',
        message_text: aiResponse,
        response_time_ms: responseTime
      })

    await sendTelegramMessage(chatId, aiResponse)

  } catch (error) {
    console.error('AI conversation error:', error)
    await sendTelegramMessage(chatId,
      "I'm having trouble processing your message right now. Please try again or use /help for available commands."
    )
  }
}

async function linkMemberAccount(chatId: number, linkingToken: string, username?: string): Promise<void> {
  const supabase = getServiceRoleClient()

  // Find member by linking token
  const { data: member, error } = await supabase
    .from('loyalty_members')
    .select('*, companies(name)')
    .eq('linking_token', linkingToken)
    .single()

  if (error || !member) {
    await sendTelegramMessage(chatId,
      '❌ Invalid linking code. Please check the code and try again, or contact support.'
    )
    return
  }

  // Get the company ID for the member we're trying to link
  const { data: targetCompany } = await supabase
    .from('loyalty_members')
    .select('company_id')
    .eq('linking_token', linkingToken)
    .single()

  if (!targetCompany?.company_id) {
    await sendTelegramMessage(chatId,
      '❌ Invalid linking code. Could not determine company. Please try again or contact support.'
    )
    return
  }

  // Check if chat_id is already linked to THIS company (not any company)
  const { data: existingBotLink } = await supabase
    .from('telegram_member_bot_links')
    .select('id, company_name:companies(name)')
    .eq('telegram_chat_id', chatId.toString())
    .eq('company_id', targetCompany.company_id)
    .eq('is_active', true)
    .maybeSingle()

  if (existingBotLink) {
    // Get company name from the nested object or use default
    const companyName = (existingBotLink.company_name as { name?: string })?.name || 'this business'
    await sendTelegramMessage(chatId,
      `❌ This Telegram account is already linked to ${companyName}.\n\n` +
      `🔄 Each Telegram account can only be linked to one account per business.`
    )
    return
  }

  // Check if the member account is already linked to another Telegram for THIS company
  const { data: memberBotLink } = await supabase
    .from('telegram_member_bot_links')
    .select('telegram_chat_id, telegram_username')
    .eq('member_id', member.id)
    .eq('company_id', targetCompany.company_id)
    .eq('is_active', true)
    .maybeSingle()

  if (memberBotLink && memberBotLink.telegram_chat_id !== chatId.toString()) {
    await sendTelegramMessage(chatId,
      `❌ This loyalty account is already linked to another Telegram account (@${memberBotLink.telegram_username || 'unknown'}) for this business.\n\n` +
      `📞 Please contact support if you need to change the linked Telegram account.`
    )
    return
  }

  // Get company info for bot configuration
  const { data: companyInfo } = await supabase
    .from('companies')
    .select('bot_tier, bot_configuration_id')
    .eq('id', targetCompany.company_id)
    .maybeSingle()

  // Create entry in telegram_member_bot_links table for multi-bot support (first)
  const botLinkData = {
    telegram_chat_id: chatId.toString(),
    telegram_username: username,
    member_id: member.id,
    company_id: targetCompany.company_id,
    bot_configuration_id: companyInfo?.bot_configuration_id || null,
    created_at: new Date().toISOString(),
    last_interaction: new Date().toISOString(),
    is_active: true
  };

  console.log('Creating bot link from webhook:', botLinkData);

  const { error: linkError } = await supabase
    .from('telegram_member_bot_links')
    .insert(botLinkData)

  if (linkError) {
    console.error('Bot link creation error:', linkError)
    console.error('Bot link data that failed in webhook:', botLinkData)
    await sendTelegramMessage(chatId,
      '❌ Failed to create bot link. Please contact support.'
    )
    return
  }

  // After link is created, update member with chat ID and clear linking token (backward compatibility)
  const { error: updateError } = await supabase
    .from('loyalty_members')
    .update({
      telegram_chat_id: chatId.toString(),
      telegram_username: username,
      linking_token: null,
      linked_at: new Date().toISOString()
    })
    .eq('id', member.id)

  if (updateError) {
    console.warn('Linked successfully, but failed to update legacy member fields:', updateError)
    // Do not fail the operation; telegram_member_bot_links is the source of truth
  }

  // Send welcome message with account info
  const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)

  // Check if member is birthday eligible
  const { data: isBirthdayEligible } = await supabase
    .rpc('is_member_birthday_eligible', { member_id: member.id })

  // Get birthday rewards count if eligible
  let birthdayMessage = ''
  if (isBirthdayEligible) {
    const { data: birthdayRewards } = await supabase
      .from('rewards')
      .select('id, title, points_required')
      .eq('company_id', member.company_id)
      .eq('reward_type', 'BIRTHDAY')
      .eq('is_active', true)
      .gte('expiration_date', new Date().toISOString())

    if (birthdayRewards && birthdayRewards.length > 0) {
      birthdayMessage = `\n🎂 <b>Special Birthday Rewards Available!</b>\n` +
        `You have ${birthdayRewards.length} birthday reward${birthdayRewards.length > 1 ? 's' : ''} waiting for you!\n` +
        `Use /rewards to see them all.\n`
    }
  }

  await sendTelegramMessage(chatId,
    `✅ <b>Account linked successfully!</b>\n\n` +
    `Welcome <b>${member.name}</b>! 👋\n` +
    `Company: ${member.companies?.name}\n` +
    `Current Points: <b>${currentPoints} points</b>\n` +
    `Loyalty Tier: <b>${member.loyalty_tier || 'Standard'}</b>${birthdayMessage}\n\n` +
    `You'll now receive loyalty updates here. Type /help to see what I can do!`,
    { parse_mode: 'HTML' }
  )

  // Log successful linking
  await supabase
    .from('telegram_notifications')
    .insert({
      member_id: member.id,
      chat_id: chatId.toString(),
      notification_type: 'account_linked',
      title: 'Account Linked',
      message: `Account linked successfully for ${member.name}`
    })
}

async function handleCashierInvitation(chatId: number, invitationToken: string): Promise<void> {
  const supabase = getServiceRoleClient()

  try {
    // Find cashier invitation by token
    const { data: invitation, error } = await supabase
      .from('cashier_invitations')
      .select(`
        *,
        companies(name),
        company_administrators!invited_by(users(name, email))
      `)
      .eq('invitation_token', invitationToken)
      .gt('expires_at', new Date().toISOString())
      .is('used_at', null)
      .single()

    if (error || !invitation) {
      await sendTelegramMessage(chatId,
        '❌ <b>Invalid or Expired Invitation</b>\n\n' +
        'This cashier invitation link is either invalid or has expired.\n\n' +
        'Please contact your employer for a new invitation link.',
        { parse_mode: 'HTML' }
      )
      return
    }

    // Check if this Telegram is already linked to a cashier for THIS company
    const { data: existingCashierLink } = await supabase
      .from('telegram_member_bot_links')
      .select(`
        id,
        loyalty_members(name, loyalty_id, role)
      `)
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', invitation.company_id)
      .eq('is_active', true)
      .maybeSingle()

    if (existingCashierLink) {
      const memberInfo = (existingCashierLink.loyalty_members as { name: string, loyalty_id: string, role?: string }[])?.[0]

      // If already linked to this company as a cashier, don't allow duplicate
      if (memberInfo?.role === 'cashier') {
        await sendTelegramMessage(chatId,
          `❌ <b>Already a Cashier</b>\n\n` +
          `This Telegram account is already registered as a cashier for this business (${memberInfo.name}, ID: ${memberInfo.loyalty_id}).\n\n` +
          `� Contact your employer if you need assistance.`,
          { parse_mode: 'HTML' }
        )
        return
      }

      // If linked as a regular member, inform they need to contact support
      if (memberInfo?.role !== 'cashier') {
        await sendTelegramMessage(chatId,
          `❌ <b>Account Already Linked</b>\n\n` +
          `This Telegram account is already linked to ${memberInfo.name} (ID: ${memberInfo.loyalty_id}) as a customer.\n\n` +
          `🔄 You cannot be both a customer and cashier for the same business.\n` +
          `📞 Contact ${(invitation.companies as { name?: string })?.name || 'your employer'} support if you need assistance.`,
          { parse_mode: 'HTML' }
        )
        return
      }
    }

    // Store chat ID in the invitation (mark as telegram connected)
    await supabase
      .from('cashier_invitations')
      .update({
        telegram_chat_id: chatId.toString(),
        telegram_sent_at: new Date().toISOString()
      })
      .eq('id', invitation.id)

    // Send welcome message for cashier invitation
    await sendTelegramMessage(chatId,
      `🎉 <b>Cashier Invitation Received!</b>\n\n` +
      `Welcome! You've been invited to be a cashier at <b>${invitation.companies.name}</b>\n\n` +
      `📧 <b>Invitation Details:</b>\n` +
      `• Email: ${invitation.email}\n` +
      `• Invited by: ${invitation.company_administrators.users.name}\n` +
      `• Company: ${invitation.companies.name}\n\n` +
      `🚀 <b>Next Steps:</b>\n` +
      `1. Check your email (${invitation.email}) for the signup link\n` +
      `2. Complete your cashier account setup\n` +
      `3. Once setup is complete, I'll help you manage cashier tasks!\n\n` +
      `💡 <b>Tip:</b> Make sure to check your spam folder if you don't see the email.`,
      { parse_mode: 'HTML' }
    )

    // Log the cashier invitation connection
    await supabase
      .from('telegram_notifications')
      .insert({
        member_id: null, // No member ID yet since they're not a member
        chat_id: chatId.toString(),
        notification_type: 'cashier_invitation_received',
        title: 'Cashier Invitation Received',
        message: `Cashier invitation received for ${invitation.companies.name}`
      })

    console.log(`[Telegram] Cashier invitation processed for ${invitation.email} at ${invitation.companies.name}`)

  } catch (error) {
    console.error('Error handling cashier invitation:', error)
    await sendTelegramMessage(chatId,
      '❌ <b>Error Processing Invitation</b>\n\n' +
      'There was a problem processing your cashier invitation.\n\n' +
      'Please contact support for assistance.',
      { parse_mode: 'HTML' }
    )
  }
}

async function sendWelcomeMessage(chatId: number): Promise<void> {
  await sendTelegramMessage(chatId,
    `🎉 <b>Welcome to the Loyalty Program Bot!</b>\n\n` +
    `I can help you manage your loyalty account, check points, and discover rewards.\n\n` +
    `To get started:\n` +
    `1. Get a linking code from your business\n` +
    `2. Use the /link command to connect your account\n\n` +
    `💡 <b>Tip:</b> Tap the menu button (☰) or type "/" to see all available commands!\n\n` +
    `Type /help for detailed command information.`,
    { parse_mode: 'HTML' }
  )
}

async function sendLinkingInstructions(chatId: number): Promise<void> {
  await sendTelegramMessage(chatId,
    `🔗 <b>Link Your Account</b>\n\n` +
    `To link your loyalty account:\n\n` +
    `1. Contact your business to get a linking code\n` +
    `2. Click the link they provide, or\n` +
    `3. Use: <code>/start YOUR_LINKING_CODE</code>\n\n` +
    `Once linked, I can help you with:\n` +
    `• Check your points balance\n` +
    `• View available rewards\n` +
    `• See transaction history\n` +
    `• Get personalized recommendations`,
    { parse_mode: 'HTML' }
  )
}

async function sendPointsBalance(chatId: number): Promise<void> {
  const botLink = await getMemberByChatId(chatId.toString())

  if (!botLink?.loyalty_members) {
    await sendTelegramMessage(chatId,
      "🔗 You need to link your account first. Use /link to get started!"
    )
    return
  }

  const member = botLink.loyalty_members

  const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)
  const pointsRatio = botLink?.companies?.points_earning_ratio || 1.0
  const ETB_PER_POINT = 1 / pointsRatio
  const estimatedValue = currentPoints * ETB_PER_POINT

  await sendTelegramMessage(chatId,
    `💎 <b>Your Points Balance</b>\n\n` +
    `Current Points: <b>${currentPoints}</b>\n` +
    `Estimated Value: <b>${estimatedValue.toFixed(2)} ETB</b>\n` +
    `Lifetime Points: ${member.lifetime_points}\n` +
    `Redeemed Points: ${member.redeemed_points || 0}\n` +
    `Loyalty Tier: <b>${member.loyalty_tier || 'Standard'}</b>\n\n` +
    `Use /rewards to see what you can redeem!`,
    { parse_mode: 'HTML' }
  )
}

async function sendAvailableRewards(chatId: number): Promise<void> {
  const supabase = getServiceRoleClient()
  const botLink = await getMemberByChatId(chatId.toString())

  if (!botLink?.loyalty_members) {
    await sendTelegramMessage(chatId,
      "🔗 You need to link your account first. Use /link to get started!"
    )
    return
  }

  const member = botLink.loyalty_members

  const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)

  // Get all rewards for the company
  const { data: allRewards } = await supabase
    .from('rewards')
    .select('*')
    .eq('company_id', member.company_id)
    .eq('is_active', true)
    .order('points_required', { ascending: true })

  if (!allRewards || allRewards.length === 0) {
    await sendTelegramMessage(chatId,
      "😔 No rewards available at the moment. Check back later!"
    )
    return
  }

  // Get already redeemed rewards for this member
  const { data: redeemedRewards } = await supabase
    .from('reward_redemptions')
    .select('reward_id')
    .eq('member_id', member.id)
    .eq('company_id', member.company_id)

  const redeemedRewardIds = new Set(redeemedRewards?.map(r => r.reward_id) || [])

  // Filter out already redeemed rewards and expired rewards
  const availableRewards = allRewards.filter(reward => {
    const isNotRedeemed = !redeemedRewardIds.has(reward.id)
    const isNotExpired = new Date(reward.expiration_date) > new Date()
    return isNotRedeemed && isNotExpired
  })

  if (availableRewards.length === 0) {
    await sendTelegramMessage(chatId,
      "🎉 You've redeemed all available rewards! Check back later for new rewards or contact your business for more options."
    )
    return
  }

  let message = `🎁 <b>Available Rewards</b>\n\n`

  availableRewards.slice(0, 10).forEach(reward => {
    const canAfford = currentPoints >= reward.points_required
    const status = canAfford ? '✅' : '⏳'

    message += `${status} <b>${reward.title}</b>\n`
    message += `   ${reward.points_required} points`
    if (reward.description) {
      message += `\n   ${reward.description}`
    }
    message += '\n\n'
  })

  message += `Your current balance: <b>${currentPoints} points</b>\n`
  message += `Contact your business to redeem rewards.`

  await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })
}

async function sendTransactionHistory(chatId: number): Promise<void> {
  const supabase = getServiceRoleClient()
  const botLink = await getMemberByChatId(chatId.toString())

  if (!botLink?.loyalty_members) {
    await sendTelegramMessage(chatId,
      "🔗 You need to link your account first. Use /link to get started!"
    )
    return
  }

  const member = botLink.loyalty_members

  const { data: transactions } = await supabase
    .from('points_transactions')
    .select('*')
    .eq('member_id', member.id)
    .order('transaction_date', { ascending: false })
    .limit(10)

  if (!transactions || transactions.length === 0) {
    await sendTelegramMessage(chatId,
      "📝 No transaction history found."
    )
    return
  }

  let message = `📝 <b>Recent Transactions</b>\n\n`

  transactions.forEach(tx => {
    const date = new Date(tx.transaction_date).toLocaleDateString()
    const type = tx.transaction_type === 'EARN' ? '📈' : '📉'
    const points = tx.transaction_type === 'EARN' ? `+${tx.points_change}` : `-${Math.abs(tx.points_change)}`

    message += `${type} ${points} points - ${date}\n`
    if (tx.description) {
      message += `   ${tx.description}\n`
    }
    message += '\n'
  })

  await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })
}

async function sendProfileInfo(chatId: number): Promise<void> {
  const botLink = await getMemberByChatId(chatId.toString())

  if (!botLink?.loyalty_members) {
    await sendTelegramMessage(chatId,
      "🔗 You need to link your account first. Use /link to get started!"
    )
    return
  }

  const member = botLink.loyalty_members

  const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)
  const joinDate = new Date(member.registration_date).toLocaleDateString()

  await sendTelegramMessage(chatId,
    `👤 <b>Your Profile</b>\n\n` +
    `Name: <b>${member.name}</b>\n` +
    `Phone: ${member.phone_number}\n` +
    `Email: ${member.email || 'Not provided'}\n` +
    `Company: ${botLink.companies?.name}\n` +
    `Member Since: ${joinDate}\n` +
    `Current Points: <b>${currentPoints}</b>\n` +
    `Loyalty Tier: <b>${member.loyalty_tier || 'Standard'}</b>\n` +
    `Telegram: @${member.telegram_username || 'Unknown'}`,
    { parse_mode: 'HTML' }
  )
}

async function sendTierInfo(chatId: number): Promise<void> {
  const supabase = getServiceRoleClient()

  try {
    // Get member details using multi-bot structure
    const botLink = await getMemberByChatId(chatId.toString())

    if (!botLink?.loyalty_members) {
      await sendTelegramMessage(chatId,
        "🔗 Please link your account first using /link"
      )
      return
    }

    const member = botLink.loyalty_members
    const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)

    // Get all tier definitions for the member's company
    const { data: tiers, error: tiersError } = await supabase
      .from('tier_definitions')
      .select('tier_name, minimum_points, benefits_description')
      .eq('company_id', member.company_id)
      .order('minimum_points', { ascending: true })

    if (tiersError) {
      console.error('Tiers lookup error:', tiersError)
      await sendTelegramMessage(chatId,
        "❌ Error retrieving tier information. Please try again later."
      )
      return
    }

    if (!tiers || tiers.length === 0) {
      await sendTelegramMessage(chatId,
        `🏆 <b>Your Tier Status</b>\n\n` +
        `Current Tier: <b>${member.loyalty_tier || 'Standard'}</b>\n` +
        `Lifetime Points: <b>${member.lifetime_points}</b>\n\n` +
        `No tier benefits configured yet. Contact support for more information.`,
        { parse_mode: 'HTML' }
      )
      return
    }

    // Find current tier based on lifetime points
    let currentTier = tiers[0] // Default to lowest tier
    let nextTier = null

    for (let i = 0; i < tiers.length; i++) {
      if (member.lifetime_points >= tiers[i].minimum_points) {
        currentTier = tiers[i]
        nextTier = tiers[i + 1] || null
      } else {
        break
      }
    }

    // Calculate progress to next tier
    let progressMessage = ''
    if (nextTier) {
      const pointsNeeded = nextTier.minimum_points - member.lifetime_points
      progressMessage = `\n\n📈 <b>Next Tier Progress</b>\n` +
                       `${pointsNeeded} more points needed for <b>${nextTier.tier_name}</b>`
    } else {
      progressMessage = `\n\n🏆 You've reached the highest tier!`
    }

    let message = `🏆 <b>Your Tier Status</b>\n\n` +
                 `Current Tier: <b>${currentTier.tier_name}</b>\n` +
                 `Lifetime Points: <b>${member.lifetime_points}</b>\n` +
                 `Available Points: <b>${currentPoints}</b>\n\n`

    // Add current tier benefits
    if (currentTier.benefits_description) {
      message += `✨ <b>Your Current Benefits:</b>\n${currentTier.benefits_description}\n`
    }

    message += progressMessage

    // Show all available tiers
    message += `\n\n📊 <b>All Available Tiers:</b>\n`
    tiers.forEach(tier => {
      const isCurrentTier = tier.tier_name === currentTier.tier_name
      const emoji = isCurrentTier ? '👑' : (member.lifetime_points >= tier.minimum_points ? '✅' : '🔒')

      message += `\n${emoji} <b>${tier.tier_name}</b> (${tier.minimum_points}+ points)\n`
      if (tier.benefits_description) {
        message += `   ${tier.benefits_description}\n`
      }
    })

    await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })

  } catch (error) {
    console.error('Error in sendTierInfo:', error)
    await sendTelegramMessage(chatId,
      "❌ Sorry, there was an error retrieving your tier information. Please try again later."
    )
  }
}

async function sendHelpMessage(chatId: number): Promise<void> {
  await sendTelegramMessage(chatId,
    `🤖 <b>Available Commands</b>\n\n` +
    `/start - Initialize bot and link account\n` +
    `/link - Get account linking instructions\n` +
    `/balance - Check your points balance\n` +
    `/tier - View your tier status and benefits\n` +
    `/rewards - Browse available rewards\n` +
    `/history - View transaction history\n` +
    `/profile - View your profile information\n` +
    `/settings - Bot preferences\n` +
    `/help - Show this help message\n` +
    `/unlink - Unlink your account\n\n` +
    `💬 <b>Natural Conversation</b>\n` +
    `You can also just ask me questions like:\n` +
    `• "How many points do I have?"\n` +
    `• "What rewards can I get?"\n` +
    `• "Show my recent transactions"\n` +
    `• "What tier am I in?"\n\n` +
    `I'm here to help! 🎉`,
    { parse_mode: 'HTML' }
  )
}

async function sendSettingsMenu(chatId: number): Promise<void> {
  await sendTelegramMessage(chatId,
    `⚙️ <b>Settings</b>\n\n` +
    `Notification preferences and other settings will be available soon.\n\n` +
    `Current notifications:\n` +
    `✅ Points earned\n` +
    `✅ Tier upgrades\n` +
    `✅ Special offers\n\n` +
    `Contact support to modify your preferences.`,
    { parse_mode: 'HTML' }
  )
}

async function handleAccountUnlink(chatId: number, username?: string): Promise<void> {
  const supabase = getServiceRoleClient()

  // Get all bot links for this chat ID
  const { data: botLinks } = await supabase
    .from('telegram_member_bot_links')
    .select(`
      id,
      member_id,
      company_id,
      companies:company_id(name),
      loyalty_members:member_id(name)
    `)
    .eq('telegram_chat_id', chatId.toString())
    .eq('is_active', true)

  if (!botLinks || botLinks.length === 0) {
    await sendTelegramMessage(chatId,
      "❌ No linked accounts found."
    )
    return
  }

  // If there's only one link, unlink it directly
  if (botLinks.length === 1) {
    const botLink = botLinks[0]
    const memberName = (botLink.loyalty_members as { name?: string })?.name || 'Unknown'
    const companyName = (botLink.companies as { name?: string })?.name || 'Unknown'

    // Log the unlink request with username for audit
    console.log(`Unlink request from ${username || 'unknown'} for member: ${memberName} at company: ${companyName}`)

    try {
      // Update the bot link to inactive
      const { error: linkError } = await supabase
        .from('telegram_member_bot_links')
        .update({ is_active: false })
        .eq('id', botLink.id)

      if (linkError) {
        console.error('Bot link update error:', linkError)
        await sendTelegramMessage(chatId,
          "❌ Failed to unlink your account. Please try again or contact support."
        )
        return
      }

      // For backward compatibility, also update the loyalty_members table
      // if this is the primary company for the member
      const { data: member } = await supabase
        .from('loyalty_members')
        .select('id, company_id')
        .eq('id', botLink.member_id)
        .single()

      if (member && member.company_id === botLink.company_id) {
        await supabase
          .from('loyalty_members')
          .update({
            telegram_chat_id: null,
            telegram_username: null,
            linking_token: null,
            linked_at: null
          })
          .eq('id', botLink.member_id)
      }

      // Send success message
      await sendTelegramMessage(chatId,
        `✅ <b>Account Successfully Unlinked</b>\n\n` +
        `Your account (${memberName}) has been disconnected from ${companyName}.\n\n` +
        `• You will no longer receive notifications from this business\n` +
        `• To reconnect, get a new link from the loyalty program admin\n\n` +
        `Thank you for using our loyalty program!`,
        { parse_mode: 'HTML' }
      )
    } catch (error) {
      console.error('Unlink error:', error)
      await sendTelegramMessage(chatId,
        "❌ An error occurred while unlinking your account. Please contact support."
      )
    }
  } else {
    // Multiple links found, ask which one to unlink
    let message = `🔄 <b>Multiple Linked Accounts</b>\n\n`
    message += `You are linked to ${botLinks.length} businesses. Which one would you like to unlink?\n\n`

    botLinks.forEach((link, index) => {
      // TypeScript fix: Use type checking to safely access nested properties
      const companyName = (link.companies as { name?: string })?.name || 'Unknown'
      message += `${index + 1}. ${companyName}\n`
    })

    message += `\nTo unlink, reply with /unlink followed by the number (e.g., /unlink 1)`

    await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })
  }
}

async function handleAccountUnlinkWithNumber(chatId: number, unlinkNumber: number, username?: string): Promise<void> {
  const supabase = getServiceRoleClient()

  // Get all bot links for this chat ID
  const { data: botLinks } = await supabase
    .from('telegram_member_bot_links')
    .select(`
      id,
      member_id,
      company_id,
      companies:company_id(name),
      loyalty_members:member_id(name)
    `)
    .eq('telegram_chat_id', chatId.toString())
    .eq('is_active', true)

  if (!botLinks || botLinks.length === 0) {
    await sendTelegramMessage(chatId,
      "❌ No linked accounts found."
    )
    return
  }

  // Check if the selected number is valid
  if (unlinkNumber < 1 || unlinkNumber > botLinks.length) {
    await sendTelegramMessage(chatId,
      `❌ Invalid selection. Please choose a number between 1 and ${botLinks.length}.`
    )
    return
  }

  // Get the selected bot link
  const botLink = botLinks[unlinkNumber - 1]
  // Get the selected bot link data
  const memberName = (botLink.loyalty_members as { name?: string })?.name || 'Unknown'
  const companyName = (botLink.companies as { name?: string })?.name || 'Unknown'

  // Log the unlink request with username for audit
  console.log(`Unlink request from ${username || 'unknown'} for member: ${memberName} at company: ${companyName}`)

  try {
    // Update the bot link to inactive
    const { error: linkError } = await supabase
      .from('telegram_member_bot_links')
      .update({ is_active: false })
      .eq('id', botLink.id)

    if (linkError) {
      console.error('Bot link update error:', linkError)
      await sendTelegramMessage(chatId,
        "❌ Failed to unlink your account. Please try again or contact support."
      )
      return
    }

    // For backward compatibility, also update the loyalty_members table
    // if this is the primary company for the member
    const { data: member } = await supabase
      .from('loyalty_members')
      .select('id, company_id')
      .eq('id', botLink.member_id)
      .single()

    if (member && member.company_id === botLink.company_id) {
      await supabase
        .from('loyalty_members')
        .update({
          telegram_chat_id: null,
          telegram_username: null,
          linking_token: null,
          linked_at: null
        })
        .eq('id', botLink.member_id)
    }

    // Send success message
    await sendTelegramMessage(chatId,
      `✅ <b>Account Successfully Unlinked</b>\n\n` +
      `Your account (${memberName}) has been disconnected from ${companyName}.\n\n` +
      `• You will no longer receive notifications from this business\n` +
      `• To reconnect, get a new link from the loyalty program admin\n\n` +
      `Thank you for using our loyalty program!`,
      { parse_mode: 'HTML' }
    )
  } catch (error) {
    console.error('Unlink error:', error)
    await sendTelegramMessage(chatId,
      "❌ An error occurred while unlinking your account. Please contact support."
    )
  }
}