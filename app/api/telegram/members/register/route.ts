import { NextRequest, NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';
import { z } from 'zod';

// Define validation schema for request body
const registrationSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  phone_number: z.string().min(10, { message: "Phone number must be at least 10 characters" }),
  email: z.string().email().nullable().optional(),
  birthday: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, { message: "Birthday must be in YYYY-MM-DD format" }),
  telegram_chat_id: z.string().min(1, { message: "Telegram ID is required" }),
  telegram_username: z.string().optional(),
  linking_token: z.string().optional(),
  company_id: z.string().uuid().optional()
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();

    const validatedData = registrationSchema.parse(body);

    // Get Supabase client
    const supabase = getServiceRoleClient();

    // If linking_token is provided, find the member by token
    let targetMember = null;
    let targetCompanyId = validatedData.company_id;

    if (validatedData.linking_token) {
      const { data: memberByToken, error: tokenError } = await supabase
        .from('loyalty_members')
        .select('id, phone_number, telegram_chat_id, company_id, name')
        .eq('linking_token', validatedData.linking_token)
        .maybeSingle();

      if (tokenError) {
        console.error('Error finding member by token:', tokenError);
        return NextResponse.json(
          { message: 'Invalid linking token' },
          { status: 400 }
        );
      }

      if (memberByToken) {
        targetMember = memberByToken;
        targetCompanyId = memberByToken.company_id;
      }
    }

    // Check if a member with this phone number or telegram ID already exists
    const { data: existingMember, error: searchError } = await supabase
      .from('loyalty_members')
      .select('id, phone_number, telegram_chat_id, company_id')
      .or(`phone_number.eq.${validatedData.phone_number},telegram_chat_id.eq.${validatedData.telegram_chat_id}`);

    if (searchError) {
      console.error('Error checking existing member:', searchError);
      return NextResponse.json(
        { message: 'Error checking membership status' },
        { status: 500 }
      );
    }

    // Handle linking existing member via token
    if (targetMember) {
      // Check if this telegram user is already linked to this company
      const { data: existingBotLink } = await supabase
        .from('telegram_member_bot_links')
        .select('id')
        .eq('telegram_chat_id', validatedData.telegram_chat_id)
        .eq('company_id', targetCompanyId)
        .eq('is_active', true)
        .maybeSingle();

      if (existingBotLink) {
        return NextResponse.json({
          message: 'This Telegram account is already linked to this business!',
          status: 'existing'
        });
      }

      // Get company and bot configuration info
      const { data: companyInfo } = await supabase
        .from('companies')
        .select('bot_tier, bot_configuration_id')
        .eq('id', targetCompanyId)
        .maybeSingle();

      // Create entry in telegram_member_bot_links table FIRST (source of truth)
      const botLinkData = {
        telegram_chat_id: validatedData.telegram_chat_id,
        telegram_username: validatedData.telegram_username,
        member_id: targetMember.id,
        company_id: targetCompanyId,
        bot_configuration_id: companyInfo?.bot_configuration_id || null,
        created_at: new Date().toISOString(),
        last_interaction: new Date().toISOString(),
        is_active: true
      };

      console.log('Creating bot link with data:', botLinkData);

      const { error: linkError } = await supabase
        .from('telegram_member_bot_links')
        .insert(botLinkData);

      if (linkError) {
        console.error('Error creating bot link:', linkError);
        console.error('Bot link data that failed:', botLinkData);
        return NextResponse.json(
          {
            message: 'Failed to link account due to a technical error. Please contact support.',
            error: linkError.message,
            debugInfo: {
              memberId: targetMember.id,
              companyId: targetCompanyId,
              chatId: validatedData.telegram_chat_id
            }
          },
          { status: 500 }
        );
      }

      // After creating the link, update the member's telegram info (backward compatibility)
      const { error: updateError } = await supabase
        .from('loyalty_members')
        .update({
          telegram_chat_id: validatedData.telegram_chat_id,
          telegram_username: validatedData.telegram_username,
          linking_token: null,
          linked_at: new Date().toISOString()
        })
        .eq('id', targetMember.id);

      if (updateError) {
        console.warn('Linked successfully, but failed to update legacy member fields:', updateError);
        // Do not fail the operation; telegram_member_bot_links is the source of truth
      }

      return NextResponse.json({
        message: 'Your account has been successfully linked to Telegram!',
        status: 'linked',
        memberId: targetMember.id
      });
    }

    if (existingMember && existingMember.length > 0) {
      // Check if this is the same telegram user but different phone, or same phone but different telegram ID
      const samePhone = existingMember.some(m => m.phone_number === validatedData.phone_number);
      const sameTelegram = existingMember.some(m => m.telegram_chat_id === validatedData.telegram_chat_id);

      if (samePhone && !sameTelegram) {
        // Update existing member with Telegram ID
        const existingMemberData = existingMember.find(m => m.phone_number === validatedData.phone_number);

        // Create bot link entry for existing member FIRST
        if (existingMemberData) {
          const { data: companyInfo } = await supabase
            .from('companies')
            .select('bot_tier, bot_configuration_id')
            .eq('id', existingMemberData.company_id)
            .maybeSingle();

          const botLinkData = {
            telegram_chat_id: validatedData.telegram_chat_id,
            telegram_username: validatedData.telegram_username,
            member_id: existingMemberData.id,
            company_id: existingMemberData.company_id,
            bot_configuration_id: companyInfo?.bot_configuration_id || null,
            created_at: new Date().toISOString(),
            last_interaction: new Date().toISOString(),
            is_active: true
          };

          console.log('Creating bot link for existing member:', botLinkData);

          const { error: existingMemberLinkError } = await supabase
            .from('telegram_member_bot_links')
            .insert(botLinkData);

          if (existingMemberLinkError) {
            console.error('Error creating bot link for existing member:', existingMemberLinkError);
            console.error('Bot link data that failed:', botLinkData);
            // Don't fail the registration for this, but log it
          }
        }

        // Then update the existing member with Telegram ID (backward compatibility)
        const { error: updateError } = await supabase
          .from('loyalty_members')
          .update({
            telegram_chat_id: validatedData.telegram_chat_id,
            telegram_username: validatedData.telegram_username,
            linked_at: new Date().toISOString()
          })
          .eq('phone_number', validatedData.phone_number);

        if (updateError) {
          console.warn('Linked successfully, but failed to update legacy member fields:', updateError);
          // Do not fail the operation; telegram_member_bot_links is the source of truth
        }

        return NextResponse.json({
          message: 'Your account has been linked to Telegram!',
          status: 'linked'
        });
      }

      // Already registered
      return NextResponse.json({
        message: 'You are already registered in our loyalty program!',
        status: 'existing'
      });
    }

    // Create new loyalty member (requires company_id for new registrations)
    if (!targetCompanyId) {
      return NextResponse.json(
        { message: 'Company ID is required for new member registration' },
        { status: 400 }
      );
    }

    const { data: newMember, error: insertError } = await supabase
      .from('loyalty_members')
      .insert({
        name: validatedData.name,
        phone_number: validatedData.phone_number,
        email: validatedData.email || null,
        birthday: validatedData.birthday,
        telegram_chat_id: validatedData.telegram_chat_id,
        telegram_username: validatedData.telegram_username,
        company_id: targetCompanyId,
        registration_date: new Date().toISOString(),
        linked_at: new Date().toISOString(),
        lifetime_points: 0,
        redeemed_points: 0,
        expired_points: 0,
        loyalty_tier: null // Will be set by DB trigger based on points
      })
      .select('id')
      .single();

    if (insertError) {
      console.error('Error creating new member:', insertError);
      return NextResponse.json(
        { message: 'Error registering your account' },
        { status: 500 }
      );
    }

    // Get company info for bot configuration
    const { data: companyInfo } = await supabase
      .from('companies')
      .select('bot_tier, bot_configuration_id')
      .eq('id', targetCompanyId)
      .maybeSingle();

    // Create entry in telegram_member_bot_links table
    const newMemberBotLinkData = {
      telegram_chat_id: validatedData.telegram_chat_id,
      telegram_username: validatedData.telegram_username,
      member_id: newMember.id,
      company_id: targetCompanyId,
      bot_configuration_id: companyInfo?.bot_configuration_id || null,
      created_at: new Date().toISOString(),
      last_interaction: new Date().toISOString(),
      is_active: true
    };

    console.log('Creating bot link for new member:', newMemberBotLinkData);

    const { error: linkError } = await supabase
      .from('telegram_member_bot_links')
      .insert(newMemberBotLinkData);

    if (linkError) {
      console.error('Error creating bot link:', linkError);
      console.error('Bot link data that failed:', newMemberBotLinkData);
      // Don't fail the registration if bot link creation fails, but return a more detailed response
      return NextResponse.json({
        message: 'Member created successfully, but there was an issue setting up the Telegram connection. Please contact support.',
        status: 'created_with_issues',
        memberId: newMember.id,
        linkError: linkError.message
      });
    }

    // Send success response with new member ID
    return NextResponse.json({
      message: 'Welcome to our loyalty program!',
      status: 'created',
      memberId: newMember.id
    });

  } catch (error) {
    console.error('Registration error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path}: ${err.message}`).join(', ');
      return NextResponse.json(
        { message: `Validation error: ${errorMessages}` },
        { status: 400 }
      );
    }

    // Handle other errors
    return NextResponse.json(
      { message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}