# Telegram Multi-Bot Member Linking Strategy

## Current Implementation Analysis

### Current Architecture

The current system has the following limitations:

1. **One-to-One Member-Bot Relationship**: A member can only be linked to one bot at a time through the `telegram_chat_id` field in the `loyalty_members` table.

2. **Bot Tier System**: Companies can have either:
   - Standard bot (shared multi-tenant `Loyal_ET_Bot`)
   - Premium bot (dedicated branded bot like `Tivoli_Cafe_bot`)

3. **Limited Cross-Bot Functionality**: Members cannot interact with multiple businesses across different bots.

4. **Database Structure Limitations**:
   - `loyalty_members` table has a single `telegram_chat_id` field
   - No table exists to track relationships between Telegram users and multiple bots

## Proposed Multi-Bot Strategy

### 1. New Database Structure

Create a new `telegram_member_bot_links` table to track many-to-many relationships:

```sql
CREATE TABLE telegram_member_bot_links (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  telegram_chat_id TEXT NOT NULL,
  telegram_username TEXT,
  member_id UUID REFERENCES loyalty_members(id) ON DELETE CASCADE,
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  bot_configuration_id UUID REFERENCES bot_configurations(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_interaction TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  preferences JSONB DEFAULT '{}'::jsonb,
  UNIQUE(telegram_chat_id, company_id)
);
```

### 2. Member Linking Rules

1. **One Business Per Bot**: A Telegram user can connect to one business per bot.
   - Example: User can connect to "edf" via their premium bot (Tivoli)
   - Same user can also connect to another business via the standard LoyalET bot

2. **Multiple Bot Support**: A member can be linked to multiple bots simultaneously.
   - Each link is tracked in the `telegram_member_bot_links` table
   - The system knows which bot context to use based on where the message originated

3. **Unique Business Constraint**: A Telegram user cannot connect to the same business through multiple bots.
   - Enforced by the UNIQUE constraint on (telegram_chat_id, company_id)

### 3. API Changes

#### Generate Link Endpoint

Update `/api/telegram/generate-link/route.ts` to:
1. Continue generating unique linking tokens
2. Return the appropriate bot link based on company configuration
3. Add metadata about which bot the link is for

#### Member Registration

Update `/api/telegram/members/register/route.ts` to:
1. Create an entry in the `telegram_member_bot_links` table
2. Maintain backward compatibility by also updating the `telegram_chat_id` in `loyalty_members`
3. Include bot configuration ID in the registration process

#### Status Endpoint

Update `/api/telegram/status/[memberId]/route.ts` to:
1. Return all bot links for a member
2. Include which bots the member is connected to
3. Maintain backward compatibility with existing fields

### 4. Migration Strategy

1. **Create New Table**: Add the `telegram_member_bot_links` table

2. **Populate Initial Data**: Migrate existing links
```sql
INSERT INTO telegram_member_bot_links (
  telegram_chat_id, 
  telegram_username, 
  member_id, 
  company_id, 
  bot_configuration_id,
  created_at,
  last_interaction
)
SELECT 
  m.telegram_chat_id,
  m.telegram_username,
  m.id,
  m.company_id,
  c.bot_configuration_id,
  COALESCE(m.linked_at, m.registration_date) as created_at,
  COALESCE(m.linked_at, m.registration_date) as last_interaction
FROM loyalty_members m
JOIN companies c ON m.company_id = c.id
WHERE m.telegram_chat_id IS NOT NULL 
  AND m.telegram_chat_id != '';
```

3. **Update API Endpoints**: Modify endpoints to use the new table while maintaining backward compatibility

4. **Update Bot Logic**: Modify bot handlers to check the new table for member relationships

### 5. User Experience

1. **Seamless Multi-Bot Experience**: Users can interact with multiple businesses through different bots without confusion.

2. **Clear Bot Identity**: Each bot maintains its brand identity and only shows information relevant to its associated business.

3. **No Cross-Contamination**: Points, rewards, and transactions remain isolated to the specific business-bot relationship.

## Implementation Plan

### Phase 1: Database Changes
- Create the new `telegram_member_bot_links` table
- Add indexes for performance optimization
- Create migration script for existing data

### Phase 2: API Updates
- Update the generate-link endpoint
- Update the member registration process
- Modify the status endpoint to return multi-bot information

### Phase 3: Bot Logic Updates
- Update webhook handlers to use the new relationship table
- Ensure proper context switching based on which bot received the message
- Update notification system to use the correct bot for each member

### Phase 4: Testing
- Test linking members to multiple bots
- Verify points and rewards isolation
- Test notifications through different bots

## Conclusion

This multi-bot strategy allows members to connect to multiple businesses through different Telegram bots while maintaining clear separation between each relationship. The solution is backward compatible with existing functionality while enabling new use cases for businesses and members.
