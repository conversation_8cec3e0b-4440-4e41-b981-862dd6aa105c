import { getServiceRoleClient } from '@/lib/supabase'
import { generateText } from 'ai'
import { google } from '@ai-sdk/google'
import { z } from 'zod'

interface BotConfig {
  id: string
  bot_token: string
  bot_username: string
  company_id: string
  companies: {
    name: string
    points_earning_ratio: number
  }
  branding_config?: {
    welcomeMessage?: string
    primaryColor?: string
    logoUrl?: string
  }
}

interface TelegramUpdate {
  message?: {
    text: string
    chat: { id: number }
    from?: { username?: string }
  }
}

export class PremiumBotHandler {
  private supabase = getServiceRoleClient()

  constructor(private botConfig: BotConfig) {}

  // Helper method to get member by chat ID using multi-bot approach
  private async getMemberByChatId(chatId: number) {
    const { data: botLink } = await this.supabase
      .from('telegram_member_bot_links')
      .select(`
        *,
        loyalty_members(
          id, name, loyalty_id, phone_number, email, birthday,
          lifetime_points, redeemed_points, expired_points, loyalty_tier,
          registration_date
        )
      `)
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', this.botConfig.company_id)
      .eq('is_active', true)
      .maybeSingle()

    if (!botLink || !botLink.loyalty_members) {
      return null
    }

    const memberArray = botLink.loyalty_members as Array<{
      id: string, name: string, loyalty_id: string, phone_number: string,
      email?: string, birthday: string, lifetime_points: number,
      redeemed_points: number, expired_points: number, loyalty_tier?: string,
      registration_date: string
    }>

    return memberArray[0] || null
  }

  async handleUpdate(update: TelegramUpdate) {
    try {
      if (!update.message?.text) {
        return
      }

      const message = update.message
      const chatId = message.chat.id
      const text = message.text
      const username = message.from?.username

      console.log(`[${this.botConfig.bot_username}] Message from ${username} (${chatId}): ${text}`)

      // Update health check
      await this.updateHealthCheck()

      if (text.startsWith('/')) {
        await this.handleCommand(chatId, text, username)
      } else {
        await this.handleConversation(chatId, text, username)
      }

    } catch (error) {
      console.error(`[${this.botConfig.bot_username}] Error handling update:`, error)
    }
  }

  private async handleCommand(chatId: number, command: string, username?: string) {
    console.log(`[${this.botConfig.bot_username}] Processing command: "${command}" for chat ${chatId}`)

    // Handle /start command with optional token
    if (command.toLowerCase().startsWith('/start')) {
      const parts = command.split(' ')
      if (parts.length > 1) {
        const token = parts[1]

        // Check if it's a business owner invitation token
        if (token.startsWith('business_invite_')) {
          const invitationToken = token.substring(16)
          await this.handleBusinessOwnerInvitation(chatId, invitationToken)
          return
        }
        // Check if it's a cashier invitation token
        else if (token.startsWith('invite_')) {
          const invitationToken = token.substring(7)
          await this.handleCashierInvitation(chatId, invitationToken)
          return
        } else {
          // Handle member linking token
          await this.linkMemberAccount(chatId, token, username)
          return
        }
      } else {
        // No token provided, send welcome message
        await this.sendBrandedWelcomeMessage(chatId)
        return
      }
    }

    switch (command.toLowerCase().trim()) {
      case '/balance':
        await this.sendPointsBalance(chatId)
        break

      case '/rewards':
        await this.sendAvailableRewards(chatId)
        break

      case '/tier':
        await this.sendTierInfo(chatId)
        break

      case '/history':
        await this.sendTransactionHistory(chatId)
        break

      case '/profile':
        await this.sendProfileInfo(chatId)
        break

      case '/help':
        await this.sendBrandedHelpMessage(chatId)
        break

      default:
        await this.sendMessage(chatId, "I don't recognize that command. Type /help to see what I can do!")
    }
  }

  private async handleConversation(chatId: number, text: string, username?: string) {
    // Get member by chat_id for this specific company using multi-bot approach
    const member = await this.getMemberByChatId(chatId)

    if (!member) {
      await this.sendMessage(chatId,
        `🔗 You need to link your ${this.botConfig.companies.name} account first. Use /start with your linking code!`
      )
      return
    }

    // Store conversation in database
    await this.supabase
      .from('telegram_conversations')
      .insert({
        member_id: member.id,
        chat_id: chatId.toString(),
        message_type: 'user',
        message_text: text,
        username: username || null
      })

    // Generate AI response with business-specific context
    try {
      const startTime = Date.now()
      const { text: aiResponse } = await generateText({
        model: google('gemini-2.0-flash'),
        system: `You are the dedicated AI assistant for ${this.botConfig.companies.name}.

        User Info:
        - Name: ${member.name}
        - Current Points: ${member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)}
        - Lifetime Points: ${member.lifetime_points}
        - Loyalty Tier: ${member.loyalty_tier || 'Standard'}
        - Business: ${this.botConfig.companies.name}

        You are the EXCLUSIVE assistant for ${this.botConfig.companies.name}. Always:
        - Refer to "${this.botConfig.companies.name}" as "us" or "we"
        - Provide personalized service for ${this.botConfig.companies.name} customers
        - Use the business name naturally in responses
        - Focus on ${this.botConfig.companies.name} loyalty program benefits

        You can help with:
        - Points balance and history for ${this.botConfig.companies.name}
        - Available rewards at ${this.botConfig.companies.name}
        - Loyalty program questions
        - Account information

        Keep responses friendly, helpful, and concise. Use emojis appropriately.
        Always maintain the premium, personalized experience for ${this.botConfig.companies.name}.`,
        messages: [{ role: 'user', content: text }],
        tools: {
          getPointsBalance: {
            description: 'Get the current points balance for the user',
            parameters: z.object({}),
            execute: async () => {
              const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)
              return {
                currentPoints,
                lifetimePoints: member.lifetime_points,
                redeemedPoints: member.redeemed_points || 0,
                tier: member.loyalty_tier || 'Standard'
              }
            }
          },
          getRecentTransactions: {
            description: 'Get recent points transactions for the user',
            parameters: z.object({
              limit: z.number().optional().default(5)
            }),
            execute: async ({ limit }) => {
              const { data: transactions } = await this.supabase
                .from('points_transactions')
                .select('*')
                .eq('member_id', member.id)
                .order('transaction_date', { ascending: false })
                .limit(limit)

              return transactions || []
            }
          },
          getAvailableRewards: {
            description: 'Get available rewards for the user',
            parameters: z.object({}),
            execute: async () => {
              const { data: rewards } = await this.supabase
                .from('rewards')
                .select('*')
                .eq('company_id', this.botConfig.company_id)
                .eq('is_active', true)
                .order('points_required', { ascending: true })

              return rewards || []
            }
          }
        },
        maxSteps: 3
      })

      const responseTime = Date.now() - startTime

      // Store AI response in database
      await this.supabase
        .from('telegram_conversations')
        .insert({
          member_id: member.id,
          chat_id: chatId.toString(),
          message_type: 'bot',
          message_text: aiResponse,
          response_time_ms: responseTime
        })

      await this.sendMessage(chatId, aiResponse)

    } catch (error) {
      console.error(`[${this.botConfig.bot_username}] AI conversation error:`, error)
      await this.sendMessage(chatId,
        `I'm having trouble processing your message right now. Please try again or use /help for available commands.`
      )
    }
  }

  private async sendBrandedWelcomeMessage(chatId: number) {
    const branding = this.botConfig.branding_config || {}
    const company = this.botConfig.companies

    const message = `🎉 ${branding.welcomeMessage || `Welcome to ${company.name}!`}

I'm your dedicated loyalty assistant for ${company.name}. I'm here to help you earn rewards and stay updated with exclusive offers!

✨ **What I can do for you:**
• Check your points balance
• Show available rewards
• Track your transaction history
• Notify you of special promotions
• Help you redeem rewards

🏆 **Get Started:**
• /balance - Check your points
• /rewards - Browse rewards
• /tier - View your status
• /help - Get assistance

Ready to start earning rewards at ${company.name}? Let's go! 🚀

---
💎 Exclusive ${company.name} Loyalty Experience`

    await this.sendMessage(chatId, message, {
      reply_markup: this.getCustomKeyboard()
    })
  }

  private async linkMemberAccount(chatId: number, linkingToken: string, username?: string) {
    // Find member by linking token for this specific company
    const { data: member, error } = await this.supabase
      .from('loyalty_members')
      .select('*, companies(name)')
      .eq('linking_token', linkingToken)
      .eq('company_id', this.botConfig.company_id)
      .single()

    if (error || !member) {
      await this.sendMessage(chatId,
        `❌ Invalid linking code for ${this.botConfig.companies.name}. Please check the code and try again, or contact ${this.botConfig.companies.name} support.`
      )
      return
    }

    // Check if chat_id is already linked to THIS company (multi-bot approach)
    const { data: existingBotLink } = await this.supabase
      .from('telegram_member_bot_links')
      .select('id, loyalty_members(name, loyalty_id)')
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', this.botConfig.company_id)
      .eq('is_active', true)
      .maybeSingle()

    if (existingBotLink) {
      const existingMemberInfo = (existingBotLink.loyalty_members as { name: string, loyalty_id: string }[])?.[0]
      await this.sendMessage(chatId,
        `❌ This Telegram account is already linked to ${existingMemberInfo?.name || 'a member'} (ID: ${existingMemberInfo?.loyalty_id || 'unknown'}) for ${this.botConfig.companies.name}.\n\n` +
        `🔄 Each Telegram account can only be linked to one account per business.\n` +
        `📞 Contact ${this.botConfig.companies.name} support if you need assistance.`
      )
      return
    }

    // Check if this member account is already linked to another Telegram for THIS company
    const { data: memberBotLink } = await this.supabase
      .from('telegram_member_bot_links')
      .select('telegram_chat_id, telegram_username')
      .eq('member_id', member.id)
      .eq('company_id', this.botConfig.company_id)
      .eq('is_active', true)
      .maybeSingle()

    if (memberBotLink && memberBotLink.telegram_chat_id !== chatId.toString()) {
      await this.sendMessage(chatId,
        `❌ This loyalty account is already linked to another Telegram account (@${memberBotLink.telegram_username || 'unknown'}) for ${this.botConfig.companies.name}.\n\n` +
        `📞 Contact ${this.botConfig.companies.name} support if you need to change the linked Telegram account.`
      )
      return
    }

    // Create entry in telegram_member_bot_links table for multi-bot support FIRST
    const botLinkData = {
      telegram_chat_id: chatId.toString(),
      telegram_username: username,
      member_id: member.id,
      company_id: this.botConfig.company_id,
      bot_configuration_id: this.botConfig.id,
      created_at: new Date().toISOString(),
      last_interaction: new Date().toISOString(),
      is_active: true
    }

    console.log(`[${this.botConfig.bot_username}] Creating bot link:`, botLinkData)

    const { error: linkError } = await this.supabase
      .from('telegram_member_bot_links')
      .insert(botLinkData)

    if (linkError) {
      console.error(`[${this.botConfig.bot_username}] Bot link creation error:`, linkError)
      console.error(`Bot link data that failed:`, botLinkData)
      await this.sendMessage(chatId,
        `❌ Failed to create bot link. Please contact ${this.botConfig.companies.name} support.`
      )
      return
    }

    // After link is created, update member with chat ID and clear linking token (backward compatibility)
    const { error: updateError } = await this.supabase
      .from('loyalty_members')
      .update({
        telegram_chat_id: chatId.toString(),
        telegram_username: username,
        linking_token: null,
        linked_at: new Date().toISOString()
      })
      .eq('id', member.id)

    if (updateError) {
      console.warn(`[${this.botConfig.bot_username}] Linked successfully, but failed to update legacy member fields:`, updateError)
      // Do not fail the operation; telegram_member_bot_links is the source of truth
    }

    // Send branded welcome message with account info
    const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)

    await this.sendMessage(chatId,
      `✅ **Account linked successfully!**\n\n` +
      `Welcome **${member.name}** to ${this.botConfig.companies.name}! 👋\n` +
      `Current Points: **${currentPoints} points**\n` +
      `Loyalty Tier: **${member.loyalty_tier || 'Standard'}**\n\n` +
      `You'll now receive loyalty updates from ${this.botConfig.companies.name} here. Type /help to see what I can do!`,
      { parse_mode: 'Markdown' }
    )
  }

  private async handleCashierInvitation(chatId: number, invitationToken: string) {
    // Handle cashier invitations for this specific company
    const { data: invitation, error } = await this.supabase
      .from('cashier_invitations')
      .select(`
        *,
        companies(name),
        company_administrators!invited_by(users(name, email))
      `)
      .eq('invitation_token', invitationToken)
      .eq('company_id', this.botConfig.company_id)
      .gt('expires_at', new Date().toISOString())
      .is('used_at', null)
      .single()

    if (error || !invitation) {
      await this.sendMessage(chatId,
        `❌ **Invalid or Expired Invitation**\n\n` +
        `This cashier invitation for ${this.botConfig.companies.name} is either invalid or has expired.\n\n` +
        `Please contact ${this.botConfig.companies.name} for a new invitation link.`,
        { parse_mode: 'Markdown' }
      )
      return
    }

    // Store chat ID in the invitation
    await this.supabase
      .from('cashier_invitations')
      .update({
        telegram_chat_id: chatId.toString(),
        telegram_sent_at: new Date().toISOString()
      })
      .eq('id', invitation.id)

    // Send branded cashier welcome message
    await this.sendMessage(chatId,
      `🎉 **Cashier Invitation from ${this.botConfig.companies.name}!**\n\n` +
      `Welcome! You've been invited to be a cashier at **${this.botConfig.companies.name}**\n\n` +
      `📧 **Invitation Details:**\n` +
      `• Email: ${invitation.email}\n` +
      `• Invited by: ${invitation.company_administrators.users.name}\n` +
      `• Company: ${this.botConfig.companies.name}\n\n` +
      `🚀 **Next Steps:**\n` +
      `1. Check your email (${invitation.email}) for the signup link\n` +
      `2. Complete your cashier account setup\n` +
      `3. Once setup is complete, I'll help you manage cashier tasks for ${this.botConfig.companies.name}!\n\n` +
      `💡 **Tip:** Make sure to check your spam folder if you don't see the email.`,
      { parse_mode: 'Markdown' }
    )
  }

  private async handleBusinessOwnerInvitation(chatId: number, invitationToken: string) {
    // Handle business owner invitations for this specific company
    const { data: invitation, error } = await this.supabase
      .from('business_owner_invitations')
      .select(`
        *,
        companies(name),
        users!invited_by(name, email)
      `)
      .eq('invitation_token', invitationToken)
      .eq('company_id', this.botConfig.company_id)
      .gt('expires_at', new Date().toISOString())
      .is('used_at', null)
      .single()

    if (error || !invitation) {
      await this.sendMessage(chatId,
        `❌ **Invalid or Expired Invitation**\n\n` +
        `This business owner invitation for ${this.botConfig.companies.name} is either invalid or has expired.\n\n` +
        `Please contact your system administrator for a new invitation link.`,
        { parse_mode: 'Markdown' }
      )
      return
    }

    // Store chat ID in the invitation
    await this.supabase
      .from('business_owner_invitations')
      .update({
        telegram_chat_id: chatId.toString(),
        telegram_sent_at: new Date().toISOString()
      })
      .eq('id', invitation.id)

    // Send branded business owner welcome message
    await this.sendMessage(chatId,
      `🎉 **Business Owner Invitation from Loyal ET!**\n\n` +
      `Welcome ${invitation.owner_name}! You've been invited to manage your business **${this.botConfig.companies.name}** on the Loyal ET platform.\n\n` +
      `📧 **Invitation Details:**\n` +
      `• Name: ${invitation.owner_name}\n` +
      `• Email: ${invitation.email}\n` +
      `• Phone: ${invitation.phone_number || 'Not provided'}\n` +
      `• Company: ${this.botConfig.companies.name}\n` +
      `• Invited by: ${invitation.users?.name || invitation.users?.email}\n\n` +
      `🚀 **Next Steps:**\n` +
      `1. Check your email (${invitation.email}) for the signup link\n` +
      `2. Complete your business owner account setup\n` +
      `3. Once setup is complete, you'll have full access to manage your business!\n\n` +
      `💼 **What you'll be able to do:**\n` +
      `• Manage loyalty programs and rewards\n` +
      `• View customer analytics and insights\n` +
      `• Configure your Telegram bot\n` +
      `• Invite and manage cashiers\n` +
      `• Monitor business performance\n\n` +
      `💡 **Tip:** Make sure to check your spam folder if you don't see the email.`,
      { parse_mode: 'Markdown' }
    )
  }

  private getCustomKeyboard() {
    const company = this.botConfig.companies

    return {
      keyboard: [
        [{ text: '💎 Check Balance' }, { text: '🎁 View Rewards' }],
        [{ text: '🏆 My Tier' }, { text: '📊 Transaction History' }],
        [{ text: '👤 My Profile' }, { text: `📱 Contact ${company.name}` }],
        [{ text: '❓ Help & Support' }]
      ],
      resize_keyboard: true,
      one_time_keyboard: false
    }
  }

  private async sendPointsBalance(chatId: number) {
    const member = await this.getMemberByChatId(chatId)

    if (!member) {
      await this.sendMessage(chatId,
        `🔗 Please link your ${this.botConfig.companies.name} account first using /start with your linking code`
      )
      return
    }

    const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)
    const pointsEarningRatio = Number(this.botConfig.companies?.points_earning_ratio) || 1.0
    const ETB_PER_POINT = 1 / pointsEarningRatio
    const estimatedValue = currentPoints * ETB_PER_POINT

    await this.sendMessage(chatId,
      `💎 **Your ${this.botConfig.companies.name} Points Balance**\n\n` +
      `Current Points: **${currentPoints}**\n` +
      `Estimated Value: **${estimatedValue.toFixed(2)} ETB**\n` +
      `Lifetime Points: ${member.lifetime_points}\n` +
      `Redeemed Points: ${member.redeemed_points || 0}\n` +
      `Loyalty Tier: **${member.loyalty_tier || 'Standard'}**\n\n` +
      `Use /rewards to see what you can redeem at ${this.botConfig.companies.name}!`,
      { parse_mode: 'Markdown' }
    )
  }

  private async sendAvailableRewards(chatId: number) {
    const { data: member } = await this.supabase
      .from('loyalty_members')
      .select('id, company_id, lifetime_points, redeemed_points, expired_points')
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', this.botConfig.company_id)
      .single()

    if (!member) {
      await this.sendMessage(chatId,
        `🔗 Please link your ${this.botConfig.companies.name} account first using /start with your linking code`
      )
      return
    }

    const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)

    // Get rewards for this specific company
    const { data: allRewards } = await this.supabase
      .from('rewards')
      .select('*')
      .eq('company_id', this.botConfig.company_id)
      .eq('is_active', true)
      .order('points_required', { ascending: true })

    if (!allRewards || allRewards.length === 0) {
      await this.sendMessage(chatId,
        `😔 No rewards available at ${this.botConfig.companies.name} at the moment. Check back later!`
      )
      return
    }

    // Get already redeemed rewards
    const { data: redeemedRewards } = await this.supabase
      .from('reward_redemptions')
      .select('reward_id')
      .eq('member_id', member.id)
      .eq('company_id', this.botConfig.company_id)

    const redeemedRewardIds = new Set(redeemedRewards?.map(r => r.reward_id) || [])

    // Filter available rewards
    const availableRewards = allRewards.filter(reward => {
      const isNotRedeemed = !redeemedRewardIds.has(reward.id)
      const isNotExpired = new Date(reward.expiration_date) > new Date()
      return isNotRedeemed && isNotExpired
    })

    if (availableRewards.length === 0) {
      await this.sendMessage(chatId,
        `🎉 You've redeemed all available rewards at ${this.botConfig.companies.name}! Check back later for new rewards.`
      )
      return
    }

    let message = `🎁 **Available Rewards at ${this.botConfig.companies.name}**\n\n`

    availableRewards.slice(0, 10).forEach(reward => {
      const canAfford = currentPoints >= reward.points_required
      const status = canAfford ? '✅' : '⏳'

      message += `${status} **${reward.title}**\n`
      message += `   ${reward.points_required} points`
      if (reward.description) {
        message += `\n   ${reward.description}`
      }
      message += '\n\n'
    })

    message += `Your current balance: **${currentPoints} points**\n`
    message += `Contact ${this.botConfig.companies.name} to redeem rewards.`

    await this.sendMessage(chatId, message, { parse_mode: 'Markdown' })
  }

  private async sendTransactionHistory(chatId: number) {
    const { data: member } = await this.supabase
      .from('loyalty_members')
      .select('id')
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', this.botConfig.company_id)
      .single()

    if (!member) {
      await this.sendMessage(chatId,
        `🔗 Please link your ${this.botConfig.companies.name} account first using /start with your linking code`
      )
      return
    }

    const { data: transactions } = await this.supabase
      .from('points_transactions')
      .select('*')
      .eq('member_id', member.id)
      .order('transaction_date', { ascending: false })
      .limit(10)

    if (!transactions || transactions.length === 0) {
      await this.sendMessage(chatId,
        `📝 No transaction history found for ${this.botConfig.companies.name}.`
      )
      return
    }

    let message = `📝 **Recent Transactions at ${this.botConfig.companies.name}**\n\n`

    transactions.forEach(tx => {
      const date = new Date(tx.transaction_date).toLocaleDateString()
      const type = tx.transaction_type === 'EARN' ? '📈' : '📉'
      const points = tx.transaction_type === 'EARN' ? `+${tx.points_change}` : `-${Math.abs(tx.points_change)}`

      message += `${type} ${points} points - ${date}\n`
      if (tx.description) {
        message += `   ${tx.description}\n`
      }
      message += '\n'
    })

    await this.sendMessage(chatId, message, { parse_mode: 'Markdown' })
  }

  private async sendProfileInfo(chatId: number) {
    const { data: member } = await this.supabase
      .from('loyalty_members')
      .select('*, companies(name)')
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', this.botConfig.company_id)
      .single()

    if (!member) {
      await this.sendMessage(chatId,
        `🔗 Please link your ${this.botConfig.companies.name} account first using /start with your linking code`
      )
      return
    }

    const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)
    const joinDate = new Date(member.registration_date).toLocaleDateString()

    await this.sendMessage(chatId,
      `👤 **Your ${this.botConfig.companies.name} Profile**\n\n` +
      `Name: **${member.name}**\n` +
      `Phone: ${member.phone_number}\n` +
      `Email: ${member.email || 'Not provided'}\n` +
      `Member Since: ${joinDate}\n` +
      `Current Points: **${currentPoints}**\n` +
      `Loyalty Tier: **${member.loyalty_tier || 'Standard'}**\n` +
      `Telegram: @${member.telegram_username || 'Unknown'}`,
      { parse_mode: 'Markdown' }
    )
  }

  private async sendTierInfo(chatId: number) {
    const { data: member } = await this.supabase
      .from('loyalty_members')
      .select('*, companies(name)')
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', this.botConfig.company_id)
      .single()

    if (!member) {
      await this.sendMessage(chatId,
        `🔗 Please link your ${this.botConfig.companies.name} account first using /start with your linking code`
      )
      return
    }

    const currentPoints = member.lifetime_points - (member.redeemed_points || 0) - (member.expired_points || 0)

    // Get tier definitions for this company
    const { data: tiers } = await this.supabase
      .from('tier_definitions')
      .select('tier_name, minimum_points, benefits_description')
      .eq('company_id', this.botConfig.company_id)
      .order('minimum_points', { ascending: true })

    if (!tiers || tiers.length === 0) {
      await this.sendMessage(chatId,
        `🏆 **Your ${this.botConfig.companies.name} Tier Status**\n\n` +
        `Current Tier: **${member.loyalty_tier || 'Standard'}**\n` +
        `Lifetime Points: **${member.lifetime_points}**\n\n` +
        `No tier benefits configured yet. Contact ${this.botConfig.companies.name} for more information.`,
        { parse_mode: 'Markdown' }
      )
      return
    }

    // Find current tier and next tier
    let currentTier = tiers[0]
    let nextTier = null

    for (let i = 0; i < tiers.length; i++) {
      if (member.lifetime_points >= tiers[i].minimum_points) {
        currentTier = tiers[i]
        nextTier = tiers[i + 1] || null
      } else {
        break
      }
    }

    // Calculate progress
    let progressMessage = ''
    if (nextTier) {
      const pointsNeeded = nextTier.minimum_points - member.lifetime_points
      progressMessage = `\n\n📈 **Next Tier Progress**\n` +
                       `${pointsNeeded} more points needed for **${nextTier.tier_name}**`
    } else {
      progressMessage = `\n\n🏆 You've reached the highest tier at ${this.botConfig.companies.name}!`
    }

    let message = `🏆 **Your ${this.botConfig.companies.name} Tier Status**\n\n` +
                 `Current Tier: **${currentTier.tier_name}**\n` +
                 `Lifetime Points: **${member.lifetime_points}**\n` +
                 `Available Points: **${currentPoints}**\n\n`

    if (currentTier.benefits_description) {
      message += `✨ **Your Current Benefits:**\n${currentTier.benefits_description}\n`
    }

    message += progressMessage

    // Show all tiers
    message += `\n\n📊 **All Tiers at ${this.botConfig.companies.name}:**\n`
    tiers.forEach(tier => {
      const isCurrentTier = tier.tier_name === currentTier.tier_name
      const emoji = isCurrentTier ? '👑' : (member.lifetime_points >= tier.minimum_points ? '✅' : '🔒')

      message += `\n${emoji} **${tier.tier_name}** (${tier.minimum_points}+ points)\n`
      if (tier.benefits_description) {
        message += `   ${tier.benefits_description}\n`
      }
    })

    await this.sendMessage(chatId, message, { parse_mode: 'Markdown' })
  }

  private async sendBrandedHelpMessage(chatId: number) {
    await this.sendMessage(chatId,
      `🤖 **${this.botConfig.companies.name} Loyalty Assistant**\n\n` +
      `**Available Commands:**\n` +
      `/start - Link your ${this.botConfig.companies.name} account\n` +
      `/balance - Check your points balance\n` +
      `/tier - View your tier status and benefits\n` +
      `/rewards - Browse available rewards\n` +
      `/history - View transaction history\n` +
      `/profile - View your profile information\n` +
      `/help - Show this help message\n\n` +
      `💬 **Natural Conversation**\n` +
      `You can also just ask me questions like:\n` +
      `• "How many points do I have?"\n` +
      `• "What rewards can I get?"\n` +
      `• "Show my recent transactions"\n` +
      `• "What tier am I in?"\n\n` +
      `I'm here to help with your ${this.botConfig.companies.name} loyalty experience! 🎉`,
      { parse_mode: 'Markdown' }
    )
  }

  private async sendMessage(chatId: number, text: string, options: Record<string, unknown> = {}) {
    try {
      const response = await fetch(`https://api.telegram.org/bot${this.botConfig.bot_token}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          chat_id: chatId,
          text,
          ...options
        })
      })

      const result = await response.json()

      if (!result.ok) {
        console.error(`[${this.botConfig.bot_username}] Failed to send message:`, result.description)
      }

    } catch (error) {
      console.error(`[${this.botConfig.bot_username}] Message send error:`, error)
    }
  }

  private async updateHealthCheck() {
    try {
      await this.supabase
        .from('bot_configurations')
        .update({
          last_health_check: new Date().toISOString()
        })
        .eq('id', this.botConfig.id)
    } catch (error) {
      console.error(`[${this.botConfig.bot_username}] Health check update failed:`, error)
    }
  }
}
