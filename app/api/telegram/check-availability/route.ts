import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

/**
 * Check if a Telegram chat ID is available for linking for a specific company
 * Now checks the telegram_member_bot_links table with (chatId, companyId)
 */
export async function POST(request: Request) {
  try {
    const { chatId, companyId } = await request.json()

    if (!chatId || !companyId) {
      return NextResponse.json({ error: 'chatId and companyId are required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Check if this chat ID is already linked to THIS company (multi-bot aware)
    const { data: existingLink, error } = await supabase
      .from('telegram_member_bot_links')
      .select('id, member_id, company_id, telegram_username, created_at, last_interaction, is_active')
      .eq('telegram_chat_id', chatId.toString())
      .eq('company_id', companyId)
      .eq('is_active', true)
      .maybeSingle()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Telegram availability check error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    const isAvailable = !existingLink

    return NextResponse.json({
      available: isAvailable,
      chatId: chatId.toString(),
      companyId,
      ...(existingLink && {
        linkedTo: {
          memberId: existingLink.member_id,
          companyId: existingLink.company_id,
          telegramUsername: existingLink.telegram_username,
          linkedAt: existingLink.created_at,
          lastInteraction: existingLink.last_interaction
        }
      })
    })

  } catch (error) {
    console.error('Telegram availability check error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
