import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ memberId: string }> }
) {
  try {
    const { memberId } = await params

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Fetch current Telegram status for the member
    const { data: member, error } = await supabase
      .from('loyalty_members')
      .select('telegram_chat_id, telegram_username, linked_at, linking_token, company_id')
      .eq('id', memberId)
      .maybeSingle()

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch member data' }, { status: 500 })
    }
    
    if (!member) {
      return NextResponse.json({ 
        isLinked: false,
        telegramChatId: null,
        telegramUsername: null,
        linkedAt: null,
        linkingToken: null,
        botLinks: [],
        totalBotLinks: 0
      })
    }

    // Fetch all bot links for this member
    const { data: botLinks } = await supabase
      .from('telegram_member_bot_links')
      .select(`
        id,
        telegram_chat_id,
        telegram_username,
        company_id,
        bot_configuration_id,
        created_at,
        last_interaction,
        is_active,
        companies(name, bot_tier),
        bot_configurations(bot_username, bot_name)
      `)
      .eq('member_id', memberId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    // Add caching headers to improve performance
    const response = NextResponse.json({
      isLinked: !!member.telegram_chat_id,
      telegramChatId: member.telegram_chat_id,
      telegramUsername: member.telegram_username,
      linkedAt: member.linked_at,
      linkingToken: member.linking_token,
      botLinks: botLinks || [],
      totalBotLinks: botLinks?.length || 0,
      // Backward compatibility - primary bot info
      primaryBot: botLinks?.find(link => link.company_id === member.company_id) || null
    })
    
    // Cache for 30 seconds - balance between freshness and performance
    response.headers.set('Cache-Control', 'public, max-age=30')
    return response

  } catch (error) {
    console.error('Telegram status error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
